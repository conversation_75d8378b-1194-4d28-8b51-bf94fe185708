//
//  FMNoteRewardAmountView.m
//  QCYZT
//  赞赏金额选择view
//  Created by shumi on 2023/1/6.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMNoteRewardAmountView.h"
#import "HttpRequestTool+Pay.h"
#import "FMNoteModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"

@interface FMNoteRewardAmountView ()<UITextFieldDelegate>
/// 支付背景view
@property (nonatomic, strong) UIView *payView;
/// 金额数据源
@property (nonatomic, strong) NSArray *amountArray;
/// 金币按钮集合
@property (nonatomic, strong) NSMutableArray *amountBtnArr;
/// 支付金额
@property (nonatomic, strong) UILabel *priceLB;
/// 余额
@property (nonatomic, strong) UILabel *balanceLB;
/// 充值按钮
@property (nonatomic, strong) UIButton *rechargeBtn;
/// 选中的支付金额
@property (nonatomic,copy) NSString *payPrice;
/// 打赏按钮
@property (nonatomic, strong) UIButton *rewardBtn;
/// 打赏金额输入框
@property (nonatomic, strong) UIView *priceInputView;
/// 金额输入框
@property (nonatomic, strong) UITextField *priceTextField;
/// 输入框确认按钮
@property (nonatomic, strong) UIButton *inputSureBtn;

@property (nonatomic, strong) ZLTagLabel *firstRechargeLabel;              // 首充优惠

// 积分相关属性
@property (nonatomic, assign) NSInteger userPoints;                        // 用户积分数量
@property (nonatomic, assign) NSInteger pointsRatio;                       // 积分兑换比例（多少积分=1金币）
@property (nonatomic, assign) NSInteger usePoints;                         // 当前使用的积分数量
@property (nonatomic, assign) BOOL pointsSelected;                         // 积分是否被选中

// 积分UI组件
@property (nonatomic, strong) UILabel *pointsTitleLabel;                   // 积分标题
@property (nonatomic, strong) UILabel *pointsNumLabel;                     // 积分数量显示
@property (nonatomic, strong) UILabel *pointsDescLabel;                    // 积分状态描述
@property (nonatomic, strong) UIButton *pointsCheckboxButton;              // 积分选择按钮

@end

@implementation FMNoteRewardAmountView

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (!self.firstRechargeLabel.hidden) {
        self.firstRechargeLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xF5CE62), ColorWithHex(0xFFE59B)] withFrame:self.firstRechargeLabel.bounds direction:GradientDirectionLeftToRight];
        [self.firstRechargeLabel layerAndBezierPathWithRect:self.firstRechargeLabel.bounds cornerRadii:CGSizeMake(12.5, 12.5) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight|UIRectCornerBottomRight];
    }
}

/// 初始化
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // 初始化积分相关属性
        [self setupPointsData];

        // 设置UI
        [self setupUI];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(userCoinUpdate:) name:kUserCoinUpdate object:nil];

    }
    return self;
}

/// 初始化积分数据
- (void)setupPointsData {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.userPoints = userModel.points;
    self.pointsRatio = 1; // 1积分=1金币
    self.usePoints = 0;
    self.pointsSelected = NO;
}

/// 设置UI
- (void)setupUI {
    
    self.backgroundColor = FMClearColor;
    
    // 调整高度以容纳积分UI（增加40像素）
    CGFloat payViewHeight = 396 + UI_SAFEAREA_BOTTOM_HEIGHT;
    UIView *payView = [[UIView alloc] initWithFrame:CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, payViewHeight)];
    payView.backgroundColor = FMWhiteColor;
    [self addSubview:payView];
    [payView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, payViewHeight) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    self.payView = payView;
    
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:ColorWithHex(0x000000) backgroundColor:FMWhiteColor numberOfLines:1];
    titleLB.text = @"选择打赏金额";
    [payView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(payView);
        make.top.equalTo(@(13));
    }];
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:ImageWithName(@"active_close") forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [payView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(titleLB);
        make.right.equalTo(@(-5));
        make.size.equalTo(@(CGSizeMake(34, 34)));
    }];
    
    UIView *line = [payView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.top.equalTo(@(50));
        make.left.right.equalTo(@(0));
        make.height.equalTo(@(0.5));
    }];
    line.backgroundColor = ColorWithHex(0xECECEC);
    
    // 设置金额选择UI
    [self setAmountChooseView:payView];
    
    // 设置支付信息UI
    [self setPayMentInfoView:payView];
    
    UIButton *rewardBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [rewardBtn setTitle:@"打赏老师" forState:UIControlStateNormal];
    [rewardBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    rewardBtn.titleLabel.font = FontWithSize(16);
    [rewardBtn setBackgroundColor:ColorWithHex(0xD70007)];
    [rewardBtn addTarget:self action:@selector(rewardBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [payView addSubview:rewardBtn];
    [rewardBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(@(-UI_SAFEAREA_BOTTOM_HEIGHT - 10 - 35));
        make.left.equalTo(@(15));
        make.right.equalTo(@(-15));
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 30, 45)));
    }];
    UI_View_Radius(rewardBtn, 45 / 2.0);
    self.rewardBtn = rewardBtn;
    
    [payView addSubview:self.firstRechargeLabel];
    [self.firstRechargeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(rewardBtn);
        make.top.equalTo(rewardBtn.mas_top).offset(-12.5);
        make.height.equalTo(25);
    }];
    self.firstRechargeLabel.hidden = YES;
    
    UILabel *tipLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x999999) backgroundColor:FMWhiteColor numberOfLines:1];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"* 打赏功能不能解锁笔记，仅作为对老师的鼓励"];
    [attrStr addAttributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : FMRedColor} range:NSMakeRange(0, 1)];
    [attrStr addAttributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : ColorWithHex(0x999999)} range:NSMakeRange(1, attrStr.length - 1)];
    tipLB.attributedText = attrStr;
    [payView addSubview:tipLB];
    [tipLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(rewardBtn.mas_bottom).offset(15);
        make.centerX.equalTo(rewardBtn);
        make.height.equalTo(20);
    }];
    
    self.payPrice = [self.amountArray firstObject];
    
    // 其他金额输入框
    [self setupPriceInputView];
}

/// 设置金额选择UI
- (void)setAmountChooseView:(UIView *)payView {
    __block UIButton *lastBtn;
    CGFloat btnWidth = ((UI_SCREEN_WIDTH - 30) - 2 * 10) / 3;
    for (NSInteger i = 0; i < self.amountArray.count; i ++) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.tag = i + 1000;
        if (i == self.amountArray.count - 1) {
            [button setTitle:self.amountArray[i] forState:UIControlStateNormal];
        } else {
            [button setTitle:[NSString stringWithFormat:@"%@金币",self.amountArray[i]] forState:UIControlStateNormal];
        }
        [button setTitleColor:ColorWithHex(0x333333) forState:UIControlStateNormal];
        [button setTitleColor:FMWhiteColor forState:UIControlStateSelected];
        if (i == 0) {
            button.selected = YES;
            [button setBackgroundColor:ColorWithHex(0xD70007)];
        } else {
            [button setBackgroundColor:ColorWithHex(0xF5F5F5)];
        }
        button.titleLabel.font = FontWithSize(16);
        UI_View_Radius(button, 5);
        [button addTarget:self action:@selector(chooseAmountPay:) forControlEvents:UIControlEventTouchUpInside];
        [payView addSubview:button];
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            if (i < 3) {
                make.top.equalTo(payView.mas_top).offset(50 + 15);
                if (i == 0) {
                    make.left.equalTo(payView.mas_left).offset(15);
                } else {
                    make.left.equalTo(lastBtn.mas_right).offset(10);
                }
            } else  {
                if (i == 3) {
                    make.top.equalTo(lastBtn.mas_bottom).offset(10);
                    make.left.equalTo(payView.mas_left).offset(15);
                } else {
                    make.left.equalTo(lastBtn.mas_right).offset(10);
                    make.centerY.equalTo(lastBtn);
                }
            }
            make.size.equalTo(@(CGSizeMake(btnWidth, 44)));
            [self.amountBtnArr addObject:button];
            lastBtn = button;
        }];
    }
}

/// 设置支付信息UI
- (void)setPayMentInfoView:(UIView *)payView {
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1];
    titleLB.text = @"金币支付:";
    [payView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(15));
        make.top.equalTo(@(50 + 88 + 40 + 10));
    }];
    
    // 支付金额
    UILabel *priceLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:ColorWithHex(0xD70007) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    priceLB.text = [NSString stringWithFormat:@"%@金币",[self.amountArray firstObject]];
    [payView addSubview:priceLB];
    [priceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(titleLB);
        make.right.equalTo(@(-15));
        make.height.equalTo(@(24));
    }];
    self.priceLB = priceLB;
    
    // 余额
    UILabel *balanceLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x666666) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    balanceLB.text = [NSString stringWithFormat:@"余额:%ld", userModel.coin];
    [payView addSubview:balanceLB];
    [balanceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(priceLB.mas_right);
        make.top.equalTo(priceLB.mas_bottom).offset(5);
    }];
    self.balanceLB = balanceLB;
    
    UIButton *rechargeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [rechargeBtn setTitle:@"做任务，赚积分>" forState:UIControlStateNormal];
    [rechargeBtn setTitleColor:ColorWithHex(0xD70007) forState:UIControlStateNormal];
    rechargeBtn.titleLabel.font = FontWithSize(14.0);
    rechargeBtn.hidden = YES;
    [rechargeBtn addTarget:self action:@selector(rechargeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [payView addSubview:rechargeBtn];
    [rechargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(balanceLB);
        make.left.equalTo(titleLB);
    }];
    self.rechargeBtn = rechargeBtn;

    // 设置积分UI
    [self setupPointsView:payView afterView:balanceLB];
}

/// 设置积分UI
- (void)setupPointsView:(UIView *)payView afterView:(UIView *)afterView {
    // 积分标题
    UILabel *pointsTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1];
    pointsTitleLabel.text = @"积分";
    [payView addSubview:pointsTitleLabel];
    [pointsTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(15));
        make.top.equalTo(afterView.mas_bottom).offset(15);
    }];
    self.pointsTitleLabel = pointsTitleLabel;

    // 积分数量显示
    UILabel *pointsNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [payView addSubview:pointsNumLabel];
    [pointsNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(pointsTitleLabel);
        make.right.equalTo(@(-50));
        make.height.equalTo(@(22));
    }];
    self.pointsNumLabel = pointsNumLabel;

    // 积分选择按钮
    UIButton *pointsCheckboxButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [pointsCheckboxButton setImage:ImageWithName(@"unchecked") forState:UIControlStateNormal];
    [pointsCheckboxButton addTarget:self action:@selector(pointsCheckboxButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [payView addSubview:pointsCheckboxButton];
    [pointsCheckboxButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(pointsTitleLabel);
        make.right.equalTo(@(-15));
        make.size.equalTo(@(CGSizeMake(20, 20)));
    }];
    self.pointsCheckboxButton = pointsCheckboxButton;

    // 积分状态描述
    UILabel *pointsDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x666666) backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [payView addSubview:pointsDescLabel];
    [pointsDescLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(pointsNumLabel.mas_right);
        make.top.equalTo(pointsNumLabel.mas_bottom).offset(5);
    }];
    self.pointsDescLabel = pointsDescLabel;
}

/// 设置金额输入框
- (void)setupPriceInputView {
    [self addSubview:self.priceInputView];
    [self.priceInputView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    
    UIView *backView = [[UIView alloc] init];
    backView.backgroundColor = FMWhiteColor;
    UI_View_Radius(backView, 8);
    [self.priceInputView addSubview:backView];
    [backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 75, 180)));
        make.center.equalTo(self.priceInputView);
    }];
    
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:ColorWithHex(0x000000) backgroundColor:FMWhiteColor numberOfLines:1];
    titleLB.text = @"输入您想打赏的金额";
    [backView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(20));
        make.centerX.equalTo(backView);
    }];
    
    UITextField *priceTextField = [[UITextField alloc] init];
    priceTextField.backgroundColor = FMWhiteColor;
    priceTextField.font = FontWithSize(16);
    priceTextField.placeholder = @"请输入金额";
    priceTextField.keyboardType = UIKeyboardTypeNumberPad;
    priceTextField.returnKeyType = UIReturnKeyDone;
    priceTextField.leftViewMode = UITextFieldViewModeAlways;
    UIView *spaceView = [[UIView alloc] init];
    spaceView.frame = CGRectMake(0, 0, 15, 44);
    priceTextField.leftView = spaceView;
    UI_View_BorderRadius(priceTextField, 5, 1, ColorWithHex(0xdddddd));
    priceTextField.delegate = self;
    [priceTextField addTarget:self action:@selector(textFieldTextDidChanged:) forControlEvents:UIControlEventEditingChanged];
    [backView addSubview:priceTextField];
    [priceTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(backView);
        make.top.equalTo(titleLB.mas_bottom).offset(15);
        make.size.equalTo(@(CGSizeMake(UI_SCREEN_WIDTH - 75 - 40, 44)));
    }];
    self.priceTextField = priceTextField;
    
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [cancelBtn setTitle:@"取消" forState:UIControlStateNormal];
    [cancelBtn setTitleColor:ColorWithHex(0x666666) forState:UIControlStateNormal];
    cancelBtn.titleLabel.font = FontWithSize(14);
    [cancelBtn addTarget:self action:@selector(cancelBtnClick) forControlEvents:UIControlEventTouchUpInside];
    UI_View_BorderRadius(cancelBtn, 18, 1, ColorWithHex(0xdddddd));
    [backView addSubview:cancelBtn];
    [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backView.mas_left).offset(42);
        make.top.equalTo(priceTextField.mas_bottom).offset(20);
        make.size.equalTo(@(CGSizeMake(100, 36)));
    }];
    
    UIButton *sureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [sureBtn setTitle:@"确定" forState:UIControlStateNormal];
    [sureBtn setTitleColor:FMWhiteColor forState:UIControlStateNormal];
    [sureBtn setBackgroundColor:ColorWithHex(0xD70007)];
    sureBtn.titleLabel.font = FontWithSize(14);
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    UI_View_Radius(sureBtn, 18);
    [backView addSubview:sureBtn];
    [sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(backView.mas_right).offset(-42);
        make.top.equalTo(priceTextField.mas_bottom).offset(20);
        make.size.equalTo(@(CGSizeMake(100, 36)));
    }];
    sureBtn.userInteractionEnabled = NO;
    [sureBtn setBackgroundColor:ColorWithHexAlpha(0xF3B2B4, 0.9)];
    self.inputSureBtn = sureBtn;
}

/// 输入框取消按钮
- (void)cancelBtnClick {
    self.priceInputView.hidden = YES;
}

/// 输入框确认按钮
- (void)sureBtnClick {
    self.payPrice = self.priceTextField.text;
    self.priceInputView.hidden = YES;
    [self updatePointsDisplay];
}

#pragma mark - 积分相关方法

/// 积分选择按钮点击
- (void)pointsCheckboxButtonTapped {
    if (![self isPointsAvailable]) {
        return;
    }

    self.pointsSelected = !self.pointsSelected;
    [self updatePointsDisplay];
    [self updatePayPriceDisplay];
}

/// 计算可抵扣的金币数量
- (NSInteger)calculateDeductibleCoins {
    NSInteger availablePoints = self.userPoints; // 可用积分
    NSInteger maxDeductibleCoins = availablePoints / self.pointsRatio; // 最大抵扣金币
    NSInteger actualOrderAmount = [self.payPrice integerValue]; // 实际订单金额
    return MIN(maxDeductibleCoins, actualOrderAmount);
}

/// 判断积分是否可用
- (BOOL)isPointsAvailable {
    // 没有选择金额或金额为0
    if ([self.payPrice integerValue] <= 0) {
        return NO;
    }

    // 用户积分不够兑换1金币
    if (self.userPoints < self.pointsRatio) {
        return NO;
    }

    return YES;
}

/// 获取当前使用的积分数量
- (NSInteger)getCurrentUsePoints {
    if (!self.pointsSelected) {
        return 0;
    }

    return [self calculateDeductibleCoins] * self.pointsRatio;
}

/// 更新积分显示
- (void)updatePointsDisplay {
    // 设置积分数量显示
    self.pointsNumLabel.text = [NSString stringWithFormat:@"%ld", (long)self.userPoints];

    if (![self isPointsAvailable]) {
        // 积分不可用状态
        if (self.userPoints < self.pointsRatio) {
            self.pointsDescLabel.text = @"暂无可用";
        } else {
            self.pointsDescLabel.text = @"暂无可用";
        }
        self.pointsDescLabel.textColor = ColorWithHex(0x999999);
        [self.pointsCheckboxButton setImage:ImageWithName(@"unchecked") forState:UIControlStateNormal];
        self.pointsCheckboxButton.userInteractionEnabled = NO;
        self.pointsSelected = NO;
    } else {
        self.pointsCheckboxButton.userInteractionEnabled = YES;
        NSInteger deductibleCoins = [self calculateDeductibleCoins];

        if (self.pointsSelected) {
            // 已选中状态
            self.pointsDescLabel.text = [NSString stringWithFormat:@"-%ld金币", (long)deductibleCoins];
            self.pointsDescLabel.textColor = ColorWithHex(0xD70007);
            [self.pointsCheckboxButton setImage:ImageWithName(@"paytype_select") forState:UIControlStateNormal];
        } else {
            // 未选中但可用状态
            self.pointsDescLabel.text = [NSString stringWithFormat:@"此单可抵扣%ld金币", (long)deductibleCoins];
            self.pointsDescLabel.textColor = ColorWithHex(0x666666);
            [self.pointsCheckboxButton setImage:ImageWithName(@"unchecked") forState:UIControlStateNormal];
        }
    }
}

/// 关闭页面
- (void)closeBtnClick {
    CGFloat payViewHeight = 396 + UI_SAFEAREA_BOTTOM_HEIGHT;
    [UIView animateWithDuration:0.3 animations:^{
        self.payView.frame = CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, payViewHeight);
        self.backgroundColor = FMClearColor;
    } completion:^(BOOL finished) {
        self.hidden = YES;
    }];
}

/// 选择支付金额
- (void)chooseAmountPay:(UIButton *)sender {
    [self.amountBtnArr enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(UIButton *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (sender.tag == obj.tag) {
            obj.selected = YES;
            obj.backgroundColor = ColorWithHex(0xD70007);
            if (idx == self.amountArray.count - 1) {
                // 弹出输入框
                self.priceInputView.hidden = NO;
                self.payPrice = @"";
                self.priceTextField.text = @"";
            } else {
                self.payPrice = self.amountArray[idx];
            }
        } else {
            obj.selected = NO;
            obj.backgroundColor = ColorWithHex(0xF5F5F5);
        }
    }];
}

/// 充值按钮点击事件
- (void)rechargeBtnClick {
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
    }];
}

/// 打赏按钮点击事件
- (void)rewardBtnClick {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if ([self.payPrice integerValue] > userModel.coin) {
        [ProtocolJump jumpWithUrl:@"qcyzt://recharge"];
    } else {
        if ([self.payPrice integerValue] > 0) {
            [self rewardPayRequest];
        } else {
            [ProtocolJump jumpWithUrl:@"qcyzt://recharge"];
        }
    }
}

/// 更新支付金额显示
- (void)updatePayPriceDisplay {
    NSInteger originalPrice = [self.payPrice integerValue];
    NSInteger deductibleCoins = self.pointsSelected ? [self calculateDeductibleCoins] : 0;
    NSInteger finalPrice = originalPrice - deductibleCoins;

    if (deductibleCoins > 0 && self.pointsSelected) {
        self.priceLB.text = [NSString stringWithFormat:@"%ld金币", (long)finalPrice];
    } else {
        self.priceLB.text = [NSString stringWithFormat:@"%@金币", self.payPrice];
    }
}

/// 支付请求
- (void)rewardPayRequest {
    self.rewardBtn.userInteractionEnabled = NO;
    NSInteger usePointsValue = [self getCurrentUsePoints];

    [HttpRequestTool payNoteRewardWithDakaId:self.noteModel.bignameDto.userId noteId:self.noteModel.noteId coin:[self.payPrice integerValue] usePoints:usePointsValue Start:^{
        [SVProgressHUD show];
    } failure:^{
        self.rewardBtn.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        self.rewardBtn.userInteractionEnabled = YES;
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showImage:nil status:@"打赏成功!"];
            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:^(NSDictionary *dic) {
                FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                self.balanceLB.text = [NSString stringWithFormat:@"余额:%ld", userModel.coin];
                // 更新积分数据
                self.userPoints = userModel.points;
                [self updatePointsDisplay];
            }];

            [self closeBtnClick];
            [[NSNotificationCenter defaultCenter] postNotificationName:KNoteRewardPaySuccess object:nil];
        } else {
            [SVProgressHUD showImage:nil status:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - noti
- (void)userCoinUpdate:(NSNotification *)noti {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.balanceLB.text = [NSString stringWithFormat:@"余额:%zd", userModel.coin];

    // 更新积分数据
    self.userPoints = userModel.points;
    [self updatePointsDisplay];

    self.payPrice = self.payPrice;
}

#pragma mark - public
- (void)show {
    self.hidden = NO;
    CGFloat payViewHeight = 396 + UI_SAFEAREA_BOTTOM_HEIGHT;
    [UIView animateWithDuration:0.3 animations:^{
        self.payView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - payViewHeight, UI_SCREEN_WIDTH, payViewHeight);
        self.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
    } completion:^(BOOL finished) {

    }];
}

#pragma mark - textFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
    //限制只能输入数字
    NSCharacterSet *cs = [[NSCharacterSet characterSetWithCharactersInString:@"0123456789"] invertedSet];
    NSString *filtered = [[string componentsSeparatedByCharactersInSet:cs] componentsJoinedByString:@""];
    if ([string isEqualToString:filtered]) {
        NSString *str = [textField.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        if (str.length > 3 && ![string isEqualToString:@""]) {
            return NO;
        }
        return YES;
    }
    return NO;
}
     
- (void)textFieldTextDidChanged:(UITextField *)textField {
    if (textField.text.length > 0 && [textField.text integerValue] > 0 && ![textField.text hasPrefix:@"0"]) {
        self.inputSureBtn.userInteractionEnabled = YES;
        [self.inputSureBtn setBackgroundColor:ColorWithHex(0xD70007)];
    } else {
        self.inputSureBtn.userInteractionEnabled = NO;
        [self.inputSureBtn setBackgroundColor:ColorWithHexAlpha(0xF3B2B4, 0.9)];
    }
}

#pragma mark - setter/getter
- (void)setPayPrice:(NSString *)payPrice {
    _payPrice = payPrice;

    // 金额变化时重置积分选择状态
    self.pointsSelected = NO;

    // 更新积分显示
    [self updatePointsDisplay];

    // 更新支付金额显示
    [self updatePayPriceDisplay];

    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSString *firstRechargeText = [FMUserDefault getSeting:AppInit_FirstRecharge_Text];

    // 计算实际需要支付的金币（扣除积分抵扣）
    NSInteger deductibleCoins = self.pointsSelected ? [self calculateDeductibleCoins] : 0;
    NSInteger finalPayAmount = [payPrice integerValue] - deductibleCoins;

    self.rechargeBtn.hidden = !(finalPayAmount > userModel.coin);

    if([payPrice integerValue] > 0) {
        [self.rewardBtn setBackgroundColor:ColorWithHex(0xD70007)];
        self.rewardBtn.userInteractionEnabled = YES;
        if (finalPayAmount > userModel.coin) {
            [self.rewardBtn setTitle:@"余额不足，请先充值" forState:UIControlStateNormal];
            if (userModel.isShowFirstDesc && firstRechargeText.length) {
                self.firstRechargeLabel.hidden = NO;
                self.firstRechargeLabel.text = firstRechargeText;
                // 此处不做延时的话layoutSubViews里无法获取firstRechargeLabel正确的frame
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self setNeedsLayout];
                    [self layoutIfNeeded];
                });
            } else {
                self.firstRechargeLabel.hidden = YES;
            }
        } else {
            [self.rewardBtn setTitle:@"打赏老师" forState:UIControlStateNormal];
            self.firstRechargeLabel.hidden = YES;
        }
    } else {
        [self.rewardBtn setTitle:@"打赏老师" forState:UIControlStateNormal];
        [self.rewardBtn setBackgroundColor:ColorWithHexAlpha(0xF3B2B4, 0.9)];
        self.rewardBtn.userInteractionEnabled = NO;
        self.firstRechargeLabel.hidden = YES;
    }
}

- (NSArray *)amountArray {
    if (!_amountArray) {
        _amountArray = @[@"2",@"5",@"8",@"18",@"88",@"其它金额"];
    }
    return _amountArray;
}

- (NSMutableArray *)amountBtnArr {
    if (!_amountBtnArr) {
        _amountBtnArr = [NSMutableArray array];
    }
    return _amountBtnArr;
}

- (UIView *)priceInputView {
    if (!_priceInputView) {
        _priceInputView = [[UIView alloc] init];
        _priceInputView.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
        _priceInputView.hidden = YES;
    }
    return _priceInputView;
}


- (ZLTagLabel *)firstRechargeLabel {
    if (!_firstRechargeLabel) {
        _firstRechargeLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x6a0000) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _firstRechargeLabel.widthPadding = 20;
    }
    
    return _firstRechargeLabel;
}

@end
