# FMNoteRewardAmountView 积分抵扣功能测试

## 功能概述
为笔记打赏功能添加了积分抵扣功能，参考PaymentView中的积分逻辑实现。

## 新增功能
1. **积分UI组件**：
   - 积分标题显示
   - 积分数量显示
   - 积分状态描述（暂无可用/可抵扣金币数/-金币）
   - 积分选择复选框

2. **积分逻辑**：
   - 积分兑换比例：1积分=1金币
   - 自动计算可抵扣的金币数量
   - 积分可用性判断
   - 选中状态管理

3. **支付集成**：
   - 支付API调用时传递积分参数
   - 实时更新支付金额显示
   - 支付成功后更新积分数据

## 测试场景

### 场景1：积分不足
- 用户积分 < 1
- 预期：显示"暂无可用"，复选框不可点击

### 场景2：积分充足，可部分抵扣
- 用户积分：5积分（可抵扣5金币）
- 打赏金额：8金币
- 预期：显示"此单可抵扣5金币"，可点击选择

### 场景3：积分充足，可完全抵扣
- 用户积分：5积分（可抵扣5金币）
- 打赏金额：2金币
- 预期：显示"此单可抵扣2金币"，选中后显示"-2金币"

### 场景4：选中积分抵扣
- 选中积分抵扣后
- 预期：
  - 支付金额显示减少
  - 积分描述显示"-X金币"
  - 复选框显示选中状态

## 界面调整
- 增加界面高度40像素以容纳积分UI
- 积分UI位于金币支付信息下方
- 保持与原有UI风格一致

## API集成
- 使用现有的`payNoteRewardWithDakaId`API
- 传递`usePoints`参数
- 支付成功后调用`queryDakaCoinsAndPointsStart`更新数据

## 注意事项
1. 积分兑换比例可通过配置调整
2. 金额变化时会重置积分选择状态
3. 支付成功后会自动更新积分数据
4. 与现有的金币余额检查逻辑兼容
